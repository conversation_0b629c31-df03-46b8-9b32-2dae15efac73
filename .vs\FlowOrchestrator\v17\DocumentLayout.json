{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Design48\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|c:\\users\\<USER>\\source\\repos\\design48\\processors\\processor.file\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|solutionrelative:processors\\processor.file\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|c:\\users\\<USER>\\source\\repos\\design48\\processors\\processor.file\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|solutionrelative:processors\\processor.file\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|c:\\users\\<USER>\\source\\repos\\design48\\processors\\processor.file\\appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{C306D9CB-FFF8-41E3-AB51-94F3E339A9BB}|Processors\\Processor.File\\Processor.File.csproj|solutionrelative:processors\\processor.file\\appsettings.production.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "appsettings.Production.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design48\\Processors\\Processor.File\\appsettings.Production.json", "RelativeDocumentMoniker": "Processors\\Processor.File\\appsettings.Production.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design48\\Processors\\Processor.File\\appsettings.Production.json", "RelativeToolTip": "Processors\\Processor.File\\appsettings.Production.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-25T06:13:54.776Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design48\\Processors\\Processor.File\\appsettings.json", "RelativeDocumentMoniker": "Processors\\Processor.File\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design48\\Processors\\Processor.File\\appsettings.json", "RelativeToolTip": "Processors\\Processor.File\\appsettings.json", "ViewState": "AgIAADYAAAAAAAAAAAAAABQAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-25T06:13:42.202Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Design48\\Processors\\Processor.File\\appsettings.Development.json", "RelativeDocumentMoniker": "Processors\\Processor.File\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Design48\\Processors\\Processor.File\\appsettings.Development.json", "RelativeToolTip": "Processors\\Processor.File\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-25T06:13:26.951Z", "EditorCaption": ""}]}]}]}